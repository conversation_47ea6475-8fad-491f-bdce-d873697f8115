
import { styled, globalCss } from '@stitches/react'
import { XAXIS_HEIGHT } from '../commons/constants';

export const globalStyles = globalCss({
    // Prevent blue highlight on all elements
    '*': {
        WebkitTapHighlightColor: 'rgba(0,0,0,0)',
        outline: 'none',
    },

    // Prevent text selection on the entire app
    'body, html, #root': {
        userSelect: 'none',
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none',
    },

    // Specific styles for Konva elements
    '.konvajs-content': {
        userSelect: 'none !important',
        WebkitUserSelect: 'none !important',
        MozUserSelect: 'none !important',
        msUserSelect: 'none !important',
        WebkitTapHighlightColor: 'rgba(0,0,0,0) !important',
        outline: 'none !important',

        '& canvas': {
            userSelect: 'none !important',
            WebkitUserSelect: 'none !important',
            MozUserSelect: 'none !important',
            msUserSelect: 'none !important',
            WebkitTapHighlightColor: 'rgba(0,0,0,0) !important',
            outline: 'none !important',
        }
    },

    // Prevent blue highlight on draggable elements
    '[draggable="true"]': {
        WebkitUserDrag: 'none',
        userDrag: 'none',
        WebkitTapHighlightColor: 'rgba(0,0,0,0)',
    }
});

export const AppContainer = styled('div', {
    position: 'relative',
    width: '100%',
    height: '100%',
    // overflow: 'hidden',
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',

    display: 'flex',
    flexDirection: 'column'
})


export const Header = styled('div', {
    position: 'absolute',
    left: 0,
    top: 0,
    width: '100%',
    height: '8vh',
    display: 'flex',
    pointerEvents: 'none',
    flexDirection: 'row',
    paddingBottom: '5px',
    paddingTop: '5px',
    alignItems: 'center',
    borderBottom: '1px dotted transparent',
    backgroundImage: 'linear-gradient(90deg, rgba(7, 81, 207, 0.7), rgba(7, 81, 207, 0.7) 20%, transparent 40%)',
    backgroundSize: '100% 1px',
    backgroundPosition: 'bottom',
    backgroundRepeat: 'no-repeat'
})

export const Footer = styled('div', {
    height: XAXIS_HEIGHT,
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',
})