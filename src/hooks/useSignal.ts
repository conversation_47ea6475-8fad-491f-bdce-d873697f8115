import { useRef, useMemo, useReducer } from 'react';

export interface TSignal<T> {
    value: T;
}

export function useSignal<T>(initial: T): TSignal<T> {
    const valueRef = useRef<T>(initial);
    const [, forceUpdate] = useReducer((x: number) => x + 1, 0);

    const signal = useMemo(() => ({
        get value() {
            return valueRef.current;
        },
        set value(newValue: T) {
            valueRef.current = newValue;
            forceUpdate();
        }
    }), []);

    return signal;
}