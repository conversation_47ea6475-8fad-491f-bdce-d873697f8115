import { Layer, Stage } from 'react-konva'
import { XAXIS_HEIGHT } from '../commons/constants'
import { useLayoutContext } from '../contexts/layoutContext'

export function XAxis() {
    const { layoutInfo } = useLayoutContext()
    return (
        <Stage
            width={layoutInfo.xAxisWidth}
            height={XAXIS_HEIGHT}
        >
            <Layer>
            </Layer>
        </Stage>
    )
}
