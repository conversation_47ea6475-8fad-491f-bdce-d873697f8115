import { XAXIS_HEIGHT, YAXIS_WIDTH } from "../commons/constants";
import { useDataContext } from "./dataContext";
import { debounce } from "es-toolkit";
import { createContext, ReactNode, useContext, useEffect, useState } from "react";

type TLayoutContext = {
    layoutInfo: {
        width: number;
        height: number;
        chartGroupHeight: number;
        xAxisWidth: number;
        chartLayoutInfoMap: Map<string, { height: number, top: number }>
    }
}


export const LayoutContext = createContext<TLayoutContext | undefined>(undefined);

export const useLayoutContext = () => {
    const context = useContext(LayoutContext);
    if (!context) {
        throw new Error('useLayoutContext must be used within an LayoutContextProvider');
    }
    return context;
}

export const LayoutContextProvider = (props: { children: ReactNode, container: React.RefObject<HTMLDivElement | null> }) => {
    const [layoutInfo, setLayoutInfo] = useState({
        width: 0,
        height: 0,
        chartGroupHeight: 0,
        chartLayoutInfoMap: new Map<string, { height: number, top: number }>(),
        xAxisWidth: 0,
    })
    const { chartStoreList } = useDataContext()

    const handleResize = debounce(() => {
        if (!props.container.current) return
        const totalFlex = 4 + (chartStoreList.length - 1);
        const chartLayoutInfoMap = new Map()
        const availHeight = props.container.current.clientHeight - XAXIS_HEIGHT;
        for (let i = 0; i < chartStoreList.length; i++) {
            const flex = i === 0 ? 4 : 1;
            const chartHeight = (flex / totalFlex) * availHeight;
            const top = i === 0 ? 0 : ((4 + i - 1) / totalFlex) * availHeight;
            chartLayoutInfoMap.set(chartStoreList[i].name, { height: chartHeight, top })
        }

        setLayoutInfo({
            width: props.container.current.clientWidth,
            height: props.container.current.clientHeight,
            chartGroupHeight: availHeight,
            chartLayoutInfoMap,
            xAxisWidth: props.container.current.clientWidth - YAXIS_WIDTH,
        })
    }, 300)

    useEffect(() => {
        if (!props.container.current) return

        const obs = new ResizeObserver(handleResize);

        obs.observe(props.container.current);

        return () => {
            if (props.container.current) {
                obs.unobserve(props.container.current);
            }
            obs.disconnect();
        };
    }, [props.container]);

    return (
        <LayoutContext.Provider value={{ layoutInfo }}>
            {props.children}
        </LayoutContext.Provider>
    );
}

