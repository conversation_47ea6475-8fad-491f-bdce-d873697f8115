
import { useAppContext } from "./appContext";
import { fetchChartDataList } from "../commons/api";
import { ChartStore } from "../store/chartStore";
import { MAIN_CHART_NAME, WEB_SOCKET_URL } from "../commons/constants";
import { createContext, ReactNode, useContext, useEffect, useRef, useState } from "react";
import { TSignal, useSignal } from "../hooks/useSignal";
import { TTicker } from "../types/common";

export type TDataContext = {
    chartStoreMap: Map<string, ChartStore> | null,
    ticker: TSignal<TTicker | null>
}

export const DataContext = createContext<TDataContext | undefined>(undefined);

export const useDataContext = () => {
    const context = useContext(DataContext);
    if (!context) {
        throw new Error('useDataContext must be used within an DataContextProvider');
    }
    return context;
}

export const DataContextProvider = (props: { children: ReactNode }) => {
    console.debug('rendering data context provider')
    const { symbol, timeframe, timeRange, timeUnit } = useAppContext()
    const [chartStoreMap, setChartStoreMap] = useState<Map<string, ChartStore> | null>(null);
    const isLoadingData = useSignal(false);
    const exhausted = useSignal(false);
    const isStreaming = useSignal(false);
    const ticker = useSignal<TTicker | null>(null)
    const wsRef = useRef<WebSocket | null>(null)

    timeRange.use()
    isLoadingData.use()
    exhausted.use()
    isStreaming.use()

    useEffect(() => {
        const { min, max } = timeRange.value
        const newMap = new Map()

        fetchChartDataList({ symbol, timeframe, startTime: min, endTime: max }).then(
            data => {
                for (const chartData of data) {
                    newMap.set(chartData.name, new ChartStore(chartData))
                }
                setChartStoreMap(newMap)
            }
        )
        return () => {
            isStreaming.value = false
            if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
                wsRef.current.close()
                wsRef.current = null
            }
        }
    }, [symbol, timeframe, wsRef])


    useEffect(() => {
        const mainChartTimeRange = chartStoreMap?.get(MAIN_CHART_NAME)?.getTimeRange()
        if (!mainChartTimeRange) return
        if (isLoadingData.value) return
        if (mainChartTimeRange.max < timeRange.value.max && (mainChartTimeRange.max + timeUnit) < Date.now()) {
            isLoadingData.value = true
            console.log('fetching newer data')
            fetchChartDataList({ symbol, timeframe, startTime: mainChartTimeRange.max + 1, limit: 200 }).then(
                data => {
                    for (const chartData of data) {
                        const chartStore = chartStoreMap?.get(chartData.name)
                        if (!chartStore) continue
                        chartStore.appendData(chartData)
                    }
                }
            ).finally(() => {
                isLoadingData.value = false
            })
        } else if (mainChartTimeRange.min > timeRange.value.min + timeUnit && !exhausted.value) {
            isLoadingData.value = true
            console.log('fetching older data')
            fetchChartDataList({ symbol, timeframe, endTime: mainChartTimeRange.min - 1, limit: 200 }).then(
                data => {
                    if (data.length === 0) {
                        exhausted.value = true
                    }
                    for (const chartData of data) {
                        const chartStore = chartStoreMap?.get(chartData.name)
                        if (!chartStore) continue
                        chartStore.prependData(chartData)
                    }
                }
            ).finally(() => {
                isLoadingData.value = false
            })
        } else if (mainChartTimeRange.max + timeUnit > Date.now()) {
            isStreaming.value = true
        }
    }, [timeRange.value, timeUnit, exhausted.value, isLoadingData.value, chartStoreMap])

    useEffect(() => {
        if (isStreaming.value && !isLoadingData.value) {
            if (wsRef.current && wsRef.current.readyState !== WebSocket.CLOSED) {
                wsRef.current.close()
                wsRef.current = null
            }
            wsRef.current = new WebSocket(`${WEB_SOCKET_URL}?topic=chartdata:${symbol}:${timeframe},ticker:${symbol}:${timeframe},position`)
            wsRef.current.onmessage = (event) => {
                if (!chartStoreMap || !chartStoreMap.size) return
                const payload = JSON.parse(event.data)
                switch (payload.topic) {
                    case `chartdata:${symbol}:${timeframe}`:
                        if (payload.updates && Array.isArray(payload.updates)) {
                            console.log(`Received ${payload.updates.length} updates for ${symbol}:${timeframe}`);
                            for (const streamData of payload.updates) {
                                for (const chartStore of chartStoreMap.values()) {
                                    const result = chartStore.updateData(streamData)
                                    if (result) break
                                }
                            }
                        }
                        break;
                    case `ticker:${symbol}:${timeframe}`:
                        if (payload.ticker) {
                            if (!ticker.value ||
                                payload.ticker.time > ticker.value.time ||
                                (payload.ticker.time === ticker.value.time && payload.ticker.price !== ticker.value.price)) {
                                ticker.value = payload.ticker
                            }
                        }
                        break;
                    case 'position':
                    // if (payload.positions && Array.isArray(payload.positions)) {
                    //     positionsRef.current = payload.positions;
                    // }
                    // break;
                    default:
                        break;
                }
            }
        } else {
            if (wsRef.current && wsRef.current.readyState !== WebSocket.CLOSED) {
                wsRef.current.close()
                wsRef.current = null
            }
        }
    }, [isStreaming.value, isLoadingData.value, wsRef, symbol, timeframe, chartStoreMap])


    return (
        <DataContext.Provider value={{ chartStoreMap, ticker }}>
            {props.children}
        </DataContext.Provider>
    );
}
