
import { useAppContext } from "./appContext";
import { fetchChartDataList } from "../commons/api";
import { ChartStore } from "../store/chartStore";
import { MAIN_CHART_NAME } from "../commons/constants";
import { useSignal } from "../hooks/useSignal";
import { createContext, ReactNode, useContext, useEffect, useState } from "react";

export type TDataContext = {
    chartStoreList: ChartStore[]
}


export const DataContext = createContext<TDataContext | undefined>(undefined);


export const useDataContext = () => {
    const context = useContext(DataContext);
    if (!context) {
        throw new Error('useDataContext must be used within an DataContextProvider');
    }
    return context;
}

export const DataContextProvider = (props: { children: ReactNode }) => {
    const { symbol, timeframe, timeRange, timeUnit } = useAppContext()
    const [chartStoreMap, setChartStoreMap] = useState(new Map<string, ChartStore>());
    const loadingData = useSignal(false);
    const exhausted = useSignal(false);

    useEffect(() => {
        const mainChartTimeRange = chartStoreMap.get(MAIN_CHART_NAME)?.getTimeRange()
        if (!mainChartTimeRange) return
        if (loadingData.value) return
        if (mainChartTimeRange.max < timeRange.value.max && (mainChartTimeRange.max + timeUnit) < Date.now()) {
            loadingData.value = true
            fetchChartDataList({ symbol, timeframe, startTime: mainChartTimeRange.max + 1 }).then(
                data => {
                    for (const chartData of data) {
                        const chartStore = chartStoreMap.get(chartData.name)
                        if (!chartStore) continue
                        chartStore.appendData(chartData)
                    }
                }
            ).finally(() => {
                loadingData.value = false
            })
        } else if (mainChartTimeRange.min > timeRange.value.min && !exhausted.value) {
            loadingData.value = true
            fetchChartDataList({ symbol, timeframe, endTime: mainChartTimeRange.min - 1 }).then(
                data => {
                    if (data.length === 0) {
                        exhausted.value = true
                    }
                    for (const chartData of data) {
                        const chartStore = chartStoreMap.get(chartData.name)
                        if (!chartStore) continue
                        chartStore.prependData(chartData)
                    }
                }
            ).finally(() => {
                loadingData.value = false
            })
        }

        return () => {
            exhausted.value = false
            loadingData.value = false
        }
    }, [timeRange.value, timeUnit])

    useEffect(() => {
        const { min, max } = timeRange.value
        const newMap = new Map()
        fetchChartDataList({ symbol, timeframe, startTime: min, endTime: max }).then(
            data => {
                for (const chartData of data) {
                    newMap.set(chartData.name, new ChartStore(chartData))
                }
                setChartStoreMap(newMap)
            }
        )
        return () => {
            chartStoreMap.clear()
        }
    }, [symbol, timeframe])

    return (
        <DataContext.Provider value={{ chartStoreList: Array.from(chartStoreMap.values()) }}>
            {props.children}
        </DataContext.Provider>
    );
}
